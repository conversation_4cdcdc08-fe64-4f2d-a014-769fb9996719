import { useState } from "react";
import { useLocation } from "react-router-dom";
import {
  TrendingUp,
  Calendar,
  BarChart3,
  Line<PERSON><PERSON>,
  Pie<PERSON>hart,
  Download,
  Settings,
  Play,
  RefreshCw,
  Database,
  Eye,
  CheckCircle,
  Target,
  ArrowUp,
  ArrowDown,
  Minus,
  Lightbulb,
  AlertTriangle,
  X,
} from "lucide-react";
import InteractiveForecastChart from "@/components/forecasting/InteractiveForecastChart";
import {
  historicalRevenueData,
  forecastRevenueData,
  forecastModels,
  forecastKPIs,
  forecastInsights,
  generateCustomForecast,
  getActiveModel,
} from "@/data/forecastMockData";

interface ForecastModel {
  id: string;
  name: string;
  type: "linear_regression" | "arima" | "lstm" | "prophet";
  accuracy: number;
  description: string;
  isActive: boolean;
}

interface ForecastResult {
  date: string;
  actual?: number;
  predicted: number;
  confidence_lower: number;
  confidence_upper: number;
}

interface ForecastMetrics {
  mae: number; // Mean Absolute Error
  rmse: number; // Root Mean Square Error
  mape: number; // Mean Absolute Percentage Error
  r2: number; // R-squared
}

export default function ForecastingPage() {
  const location = useLocation();
  const selectedDatasetId = location.state?.selectedDataset;

  const [models] = useState(forecastModels);
  const [forecastPeriod, setForecastPeriod] = useState("30");
  const [targetMetric, setTargetMetric] = useState("revenue");
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentHistoricalData, setCurrentHistoricalData] = useState(
    historicalRevenueData
  );
  const [currentForecastData, setCurrentForecastData] =
    useState(forecastRevenueData);
  const [activeModel, setActiveModel] = useState(getActiveModel());
  const [showSettings, setShowSettings] = useState(false);
  const [showInsights, setShowInsights] = useState(false);

  const handleGenerateForecast = () => {
    setIsGenerating(true);
    setProgress(0);

    // Simulate forecast generation
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + Math.random() * 15;
        if (newProgress >= 100) {
          setIsGenerating(false);

          // Generate new forecast based on current settings
          const { historical, forecast } = generateCustomForecast(
            parseInt(forecastPeriod),
            targetMetric,
            activeModel.type
          );

          setCurrentHistoricalData(historical);
          setCurrentForecastData(forecast);

          clearInterval(interval);
          return 100;
        }
        return newProgress;
      });
    }, 200);
  };

  const getModelIcon = (type: string) => {
    switch (type) {
      case "linear_regression":
        return <LineChart size={16} style={{ color: "#2563eb" }} />;
      case "arima":
        return <TrendingUp size={16} style={{ color: "#16a34a" }} />;
      case "lstm":
        return <BarChart3 size={16} style={{ color: "#7c3aed" }} />;
      case "prophet":
        return <PieChart size={16} style={{ color: "#d97706" }} />;
      default:
        return <TrendingUp size={16} />;
    }
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous)
      return <ArrowUp size={14} style={{ color: "#16a34a" }} />;
    if (current < previous)
      return <ArrowDown size={14} style={{ color: "#dc2626" }} />;
    return <Minus size={14} style={{ color: "#71717a" }} />;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-CA", {
      style: "currency",
      currency: "CAD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <div style={{ padding: "1.5rem" }}>
      {/* Header */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "2rem",
        }}
      >
        <div>
          <h1
            style={{
              fontSize: "1.875rem",
              fontWeight: "600",
              color: "#18181b",
              marginBottom: "0.5rem",
            }}
          >
            Sales Forecasting
          </h1>
          <p style={{ color: "#3f3f46" }}>
            Generate intelligent forecasts using advanced machine learning
            models
          </p>
        </div>
        <div style={{ display: "flex", gap: "1rem" }}>
          <button
            onClick={() => setShowInsights(true)}
            style={{
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
              backgroundColor: "#ab732b",
              color: "white",
              borderRadius: "0.375rem",
              padding: "0.625rem 1rem",
              fontWeight: "500",
              fontSize: "0.875rem",
              border: "none",
              cursor: "pointer",
            }}
          >
            <Lightbulb size={16} />
            Insights
          </button>
          <button
            onClick={() => setShowSettings(true)}
            style={{
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
              backgroundColor: "#e4e4e7",
              color: "#3f3f46",
              borderRadius: "0.375rem",
              padding: "0.625rem 1rem",
              fontWeight: "500",
              fontSize: "0.875rem",
              border: "none",
              cursor: "pointer",
            }}
          >
            <Settings size={16} />
            Settings
          </button>
          <button
            onClick={handleGenerateForecast}
            disabled={isGenerating}
            style={{
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
              backgroundColor: isGenerating ? "#d1d5db" : "#2b524f",
              color: "white",
              borderRadius: "0.375rem",
              padding: "0.625rem 1rem",
              fontWeight: "500",
              fontSize: "0.875rem",
              border: "none",
              cursor: isGenerating ? "not-allowed" : "pointer",
            }}
          >
            {isGenerating ? (
              <>
                <RefreshCw size={16} className="animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Play size={16} />
                Generate Forecast
              </>
            )}
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {isGenerating && (
        <div
          style={{
            backgroundColor: "white",
            borderRadius: "0.5rem",
            border: "1px solid #e4e4e7",
            padding: "1.5rem",
            marginBottom: "1.5rem",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              fontSize: "0.875rem",
              color: "#71717a",
              marginBottom: "0.5rem",
            }}
          >
            <span>Generating Forecast</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div
            style={{
              height: "0.5rem",
              backgroundColor: "#f1f5f9",
              borderRadius: "0.25rem",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                height: "100%",
                backgroundColor: "#2b524f",
                width: `${progress}%`,
                transition: "width 0.3s ease",
              }}
            />
          </div>
          <div
            style={{
              fontSize: "0.75rem",
              color: "#71717a",
              marginTop: "0.5rem",
            }}
          >
            Processing {forecastPeriod} days using ARIMA model...
          </div>
        </div>
      )}

      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr 300px",
          gap: "2rem",
        }}
      >
        {/* Main Content */}
        <div>
          {/* Quick Stats */}
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(4, 1fr)",
              gap: "1rem",
              marginBottom: "2rem",
            }}
          >
            <div
              style={{
                backgroundColor: "white",
                borderRadius: "0.5rem",
                border: "1px solid #e4e4e7",
                padding: "1.5rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: "0.5rem",
                }}
              >
                <div
                  style={{
                    fontSize: "0.875rem",
                    color: "#71717a",
                    fontWeight: "500",
                  }}
                >
                  Predicted Revenue
                </div>
                <TrendingUp size={16} style={{ color: "#16a34a" }} />
              </div>
              <div
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#18181b",
                }}
              >
                {formatCurrency(forecastKPIs.totalRevenueForecast.current)}
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.25rem",
                  fontSize: "0.75rem",
                  color: "#16a34a",
                }}
              >
                {getTrendIcon(
                  forecastKPIs.totalRevenueForecast.current,
                  forecastKPIs.totalRevenueForecast.previous
                )}
                +{forecastKPIs.totalRevenueForecast.change}% vs last period
              </div>
            </div>

            <div
              style={{
                backgroundColor: "white",
                borderRadius: "0.5rem",
                border: "1px solid #e4e4e7",
                padding: "1.5rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: "0.5rem",
                }}
              >
                <div
                  style={{
                    fontSize: "0.875rem",
                    color: "#71717a",
                    fontWeight: "500",
                  }}
                >
                  Model Accuracy
                </div>
                <Target size={16} style={{ color: "#2b524f" }} />
              </div>
              <div
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#18181b",
                }}
              >
                {formatPercentage(activeModel.accuracy)}
              </div>
              <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                {activeModel.name}
              </div>
            </div>

            <div
              style={{
                backgroundColor: "white",
                borderRadius: "0.5rem",
                border: "1px solid #e4e4e7",
                padding: "1.5rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: "0.5rem",
                }}
              >
                <div
                  style={{
                    fontSize: "0.875rem",
                    color: "#71717a",
                    fontWeight: "500",
                  }}
                >
                  Forecast Period
                </div>
                <Calendar size={16} style={{ color: "#ab732b" }} />
              </div>
              <div
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#18181b",
                }}
              >
                {forecastPeriod} Days
              </div>
              <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                Jan 8 - Feb 7, 2024
              </div>
            </div>

            <div
              style={{
                backgroundColor: "white",
                borderRadius: "0.5rem",
                border: "1px solid #e4e4e7",
                padding: "1.5rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: "0.5rem",
                }}
              >
                <div
                  style={{
                    fontSize: "0.875rem",
                    color: "#71717a",
                    fontWeight: "500",
                  }}
                >
                  Avg Order Value
                </div>
                <CheckCircle size={16} style={{ color: "#16a34a" }} />
              </div>
              <div
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#18181b",
                }}
              >
                {formatCurrency(forecastKPIs.averageOrderValue.current)}
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.25rem",
                  fontSize: "0.75rem",
                  color: "#16a34a",
                }}
              >
                {getTrendIcon(
                  forecastKPIs.averageOrderValue.current,
                  forecastKPIs.averageOrderValue.previous
                )}
                +{forecastKPIs.averageOrderValue.change}% projected
              </div>
            </div>
          </div>

          {/* Chart Area */}
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              border: "1px solid #e4e4e7",
              padding: "1.5rem",
              marginBottom: "2rem",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "1.5rem",
              }}
            >
              <h3
                style={{
                  fontSize: "1.125rem",
                  fontWeight: "600",
                  color: "#18181b",
                  margin: 0,
                }}
              >
                Revenue Forecast Visualization
              </h3>
              <div style={{ display: "flex", gap: "1rem" }}>
                <button
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    backgroundColor: "#f8fafc",
                    color: "#374151",
                    borderRadius: "0.375rem",
                    padding: "0.5rem 0.75rem",
                    fontWeight: "500",
                    fontSize: "0.75rem",
                    border: "1px solid #e2e8f0",
                    cursor: "pointer",
                  }}
                >
                  <Eye size={12} />
                  View Details
                </button>
                <button
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                    backgroundColor: "#ab732b",
                    color: "white",
                    borderRadius: "0.375rem",
                    padding: "0.5rem 0.75rem",
                    fontWeight: "500",
                    fontSize: "0.75rem",
                    border: "none",
                    cursor: "pointer",
                  }}
                >
                  <Download size={12} />
                  Export
                </button>
              </div>
            </div>

            {/* Interactive Chart */}
            <InteractiveForecastChart
              historicalData={currentHistoricalData}
              forecastData={currentForecastData}
              isLoading={isGenerating}
            />
          </div>

          {/* Forecast Table */}
          {currentForecastData.length > 0 && (
            <div
              style={{
                backgroundColor: "white",
                borderRadius: "0.5rem",
                border: "1px solid #e4e4e7",
                padding: "1.5rem",
              }}
            >
              <h3
                style={{
                  fontSize: "1.125rem",
                  fontWeight: "600",
                  color: "#18181b",
                  marginBottom: "1rem",
                }}
              >
                Detailed Forecast Results
              </h3>
              <div style={{ overflowX: "auto" }}>
                <table style={{ width: "100%", fontSize: "0.875rem" }}>
                  <thead>
                    <tr style={{ borderBottom: "1px solid #e2e8f0" }}>
                      <th
                        style={{
                          textAlign: "left",
                          padding: "0.75rem",
                          color: "#374151",
                          fontWeight: "600",
                        }}
                      >
                        Date
                      </th>
                      <th
                        style={{
                          textAlign: "right",
                          padding: "0.75rem",
                          color: "#374151",
                          fontWeight: "600",
                        }}
                      >
                        Predicted Revenue
                      </th>
                      <th
                        style={{
                          textAlign: "right",
                          padding: "0.75rem",
                          color: "#374151",
                          fontWeight: "600",
                        }}
                      >
                        Lower Bound
                      </th>
                      <th
                        style={{
                          textAlign: "right",
                          padding: "0.75rem",
                          color: "#374151",
                          fontWeight: "600",
                        }}
                      >
                        Upper Bound
                      </th>
                      <th
                        style={{
                          textAlign: "right",
                          padding: "0.75rem",
                          color: "#374151",
                          fontWeight: "600",
                        }}
                      >
                        Confidence
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentForecastData.slice(0, 7).map((item, index) => {
                      const confidence =
                        ((item.predicted - item.confidence_lower) /
                          item.predicted) *
                        100;
                      return (
                        <tr
                          key={index}
                          style={{ borderBottom: "1px solid #f1f5f9" }}
                        >
                          <td style={{ padding: "0.75rem", color: "#1f2937" }}>
                            {new Date(item.date).toLocaleDateString()}
                          </td>
                          <td
                            style={{
                              padding: "0.75rem",
                              color: "#1f2937",
                              textAlign: "right",
                              fontWeight: "600",
                            }}
                          >
                            {formatCurrency(item.predicted)}
                          </td>
                          <td
                            style={{
                              padding: "0.75rem",
                              color: "#71717a",
                              textAlign: "right",
                            }}
                          >
                            {formatCurrency(item.confidence_lower)}
                          </td>
                          <td
                            style={{
                              padding: "0.75rem",
                              color: "#71717a",
                              textAlign: "right",
                            }}
                          >
                            {formatCurrency(item.confidence_upper)}
                          </td>
                          <td
                            style={{ padding: "0.75rem", textAlign: "right" }}
                          >
                            <span
                              style={{
                                backgroundColor:
                                  confidence > 85
                                    ? "#dcfce7"
                                    : confidence > 70
                                      ? "#fef3c7"
                                      : "#fef2f2",
                                color:
                                  confidence > 85
                                    ? "#166534"
                                    : confidence > 70
                                      ? "#d97706"
                                      : "#dc2626",
                                padding: "0.25rem 0.5rem",
                                borderRadius: "0.375rem",
                                fontSize: "0.75rem",
                                fontWeight: "500",
                              }}
                            >
                              {formatPercentage(confidence)}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div>
          {/* Model Selection */}
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              border: "1px solid #e4e4e7",
              padding: "1.5rem",
              marginBottom: "1.5rem",
            }}
          >
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Forecasting Models
            </h3>
            {/* <div style={{ space: "0.75rem" }}> */}
            <div style={{ display: "flex", gap: "0.75rem" }}>
              {models.map((model) => (
                <div
                  key={model.id}
                  style={{
                    padding: "1rem",
                    border: `1px solid ${model.isActive ? "#2b524f" : "#e4e4e7"}`,
                    borderRadius: "0.375rem",
                    backgroundColor: model.isActive ? "#f0fdf4" : "white",
                    cursor: "pointer",
                    transition: "all 0.2s",
                    marginBottom: "0.75rem",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      marginBottom: "0.5rem",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "0.5rem",
                      }}
                    >
                      {getModelIcon(model.type)}
                      <span
                        style={{
                          fontWeight: "500",
                          color: "#18181b",
                          fontSize: "0.875rem",
                        }}
                      >
                        {model.name}
                      </span>
                    </div>
                    {model.isActive && (
                      <CheckCircle size={14} style={{ color: "#16a34a" }} />
                    )}
                  </div>
                  <div
                    style={{
                      fontSize: "0.75rem",
                      color: "#71717a",
                      marginBottom: "0.5rem",
                    }}
                  >
                    {model.description}
                  </div>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <span style={{ fontSize: "0.75rem", color: "#71717a" }}>
                      Accuracy
                    </span>
                    <span
                      style={{
                        fontSize: "0.75rem",
                        fontWeight: "600",
                        color:
                          model.accuracy > 90
                            ? "#16a34a"
                            : model.accuracy > 80
                              ? "#d97706"
                              : "#dc2626",
                      }}
                    >
                      {formatPercentage(model.accuracy)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Configuration */}
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              border: "1px solid #e4e4e7",
              padding: "1.5rem",
              marginBottom: "1.5rem",
            }}
          >
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Forecast Configuration
            </h3>

            <div style={{ marginBottom: "1rem" }}>
              <label
                style={{
                  display: "block",
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "#3f3f46",
                  marginBottom: "0.25rem",
                }}
              >
                Forecast Period
              </label>
              <select
                value={forecastPeriod}
                onChange={(e) => setForecastPeriod(e.target.value)}
                style={{
                  width: "100%",
                  padding: "0.625rem 0.75rem",
                  border: "1px solid #e4e4e7",
                  borderRadius: "0.375rem",
                  backgroundColor: "white",
                  color: "#18181b",
                  fontSize: "0.875rem",
                  outline: "none",
                }}
              >
                <option value="7">7 Days</option>
                <option value="14">14 Days</option>
                <option value="30">30 Days</option>
                <option value="60">60 Days</option>
                <option value="90">90 Days</option>
              </select>
            </div>

            <div style={{ marginBottom: "1rem" }}>
              <label
                style={{
                  display: "block",
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  color: "#3f3f46",
                  marginBottom: "0.25rem",
                }}
              >
                Target Metric
              </label>
              <select
                value={targetMetric}
                onChange={(e) => setTargetMetric(e.target.value)}
                style={{
                  width: "100%",
                  padding: "0.625rem 0.75rem",
                  border: "1px solid #e4e4e7",
                  borderRadius: "0.375rem",
                  backgroundColor: "white",
                  color: "#18181b",
                  fontSize: "0.875rem",
                  outline: "none",
                }}
              >
                <option value="revenue">Total Revenue</option>
                <option value="units">Units Sold</option>
                <option value="transactions">Transactions</option>
                <option value="customers">New Customers</option>
              </select>
            </div>

            <div
              style={{
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                borderRadius: "0.375rem",
                padding: "0.75rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.5rem",
                  marginBottom: "0.5rem",
                }}
              >
                <Database size={14} style={{ color: "#2b524f" }} />
                <span
                  style={{
                    fontSize: "0.875rem",
                    fontWeight: "500",
                    color: "#374151",
                  }}
                >
                  Data Source
                </span>
              </div>
              <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                Q4 Sales Data - Toronto Downtown
                <br />
                1,198 records • Updated 2 hours ago
              </div>
            </div>
          </div>

          {/* Model Performance */}
          {activeModel && (
            <div
              style={{
                backgroundColor: "white",
                borderRadius: "0.5rem",
                border: "1px solid #e4e4e7",
                padding: "1.5rem",
              }}
            >
              <h3
                style={{
                  fontSize: "1.125rem",
                  fontWeight: "600",
                  color: "#18181b",
                  marginBottom: "1rem",
                }}
              >
                Model Performance
              </h3>

              {/* <div style={{ space: "0.75rem" }}> */}
              <div style={{ display: "flex", gap: "0.75rem" }}>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "0.5rem",
                  }}
                >
                  <span style={{ fontSize: "0.875rem", color: "#71717a" }}>
                    Mean Absolute Error
                  </span>
                  <span
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "600",
                      color: "#18181b",
                    }}
                  >
                    {formatCurrency(activeModel.metrics.mae)}
                  </span>
                </div>

                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "0.5rem",
                  }}
                >
                  <span style={{ fontSize: "0.875rem", color: "#71717a" }}>
                    Root Mean Square Error
                  </span>
                  <span
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "600",
                      color: "#18181b",
                    }}
                  >
                    {formatCurrency(activeModel.metrics.rmse)}
                  </span>
                </div>

                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "0.5rem",
                  }}
                >
                  <span style={{ fontSize: "0.875rem", color: "#71717a" }}>
                    Mean Absolute Percentage Error
                  </span>
                  <span
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "600",
                      color: "#18181b",
                    }}
                  >
                    {formatPercentage(activeModel.metrics.mape)}
                  </span>
                </div>

                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <span style={{ fontSize: "0.875rem", color: "#71717a" }}>
                    R-squared
                  </span>
                  <span
                    style={{
                      fontSize: "0.875rem",
                      fontWeight: "600",
                      color: "#16a34a",
                    }}
                  >
                    {activeModel.metrics.r2.toFixed(3)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Insights Modal */}
      {showInsights && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              padding: "2rem",
              maxWidth: "700px",
              width: "90%",
              maxHeight: "90vh",
              overflow: "auto",
              boxShadow:
                "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "2rem",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "0.75rem",
                }}
              >
                <Lightbulb size={24} style={{ color: "#ab732b" }} />
                <h2
                  style={{
                    fontSize: "1.5rem",
                    fontWeight: "600",
                    color: "#18181b",
                    margin: 0,
                  }}
                >
                  Forecast Insights
                </h2>
              </div>
              <button
                onClick={() => setShowInsights(false)}
                style={{
                  padding: "0.5rem",
                  color: "#71717a",
                  backgroundColor: "transparent",
                  border: "none",
                  borderRadius: "0.375rem",
                  cursor: "pointer",
                  transition: "all 0.2s",
                }}
                onMouseOver={(e: any) => {
                  e.target.style.backgroundColor = "#f4f4f5";
                  e.target.style.color = "#18181b";
                }}
                onMouseOut={(e: any) => {
                  e.target.style.backgroundColor = "transparent";
                  e.target.style.color = "#71717a";
                }}
              >
                <X size={20} />
              </button>
            </div>

            {/* <div style={{ space: "1.5rem" }}> */}
            <div style={{ display: "flex", gap: "1.5rem" }}>
              {forecastInsights.map((insight, index) => (
                <div
                  key={insight.id}
                  style={{
                    backgroundColor: "#f8fafc",
                    border: "1px solid #e2e8f0",
                    borderRadius: "0.5rem",
                    padding: "1.5rem",
                    marginBottom:
                      index < forecastInsights.length - 1 ? "1.5rem" : "0",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "start",
                      marginBottom: "1rem",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "0.75rem",
                      }}
                    >
                      {insight.type === "trend" && (
                        <TrendingUp size={20} style={{ color: "#16a34a" }} />
                      )}
                      {insight.type === "opportunity" && (
                        <Target size={20} style={{ color: "#2b524f" }} />
                      )}
                      {insight.type === "improvement" && (
                        <ArrowUp size={20} style={{ color: "#ab732b" }} />
                      )}
                      {insight.type === "prediction" && (
                        <Eye size={20} style={{ color: "#7c3aed" }} />
                      )}
                      {insight.type === "validation" && (
                        <CheckCircle size={20} style={{ color: "#16a34a" }} />
                      )}
                      <h3
                        style={{
                          fontSize: "1.125rem",
                          fontWeight: "600",
                          color: "#18181b",
                          margin: 0,
                        }}
                      >
                        {insight.title}
                      </h3>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "0.5rem",
                      }}
                    >
                      <span
                        style={{
                          padding: "0.25rem 0.75rem",
                          borderRadius: "9999px",
                          fontSize: "0.75rem",
                          fontWeight: "500",
                          textTransform: "capitalize",
                          backgroundColor:
                            insight.priority === "high"
                              ? "#fef2f2"
                              : insight.priority === "medium"
                                ? "#fef3c7"
                                : "#f0fdf4",
                          color:
                            insight.priority === "high"
                              ? "#dc2626"
                              : insight.priority === "medium"
                                ? "#d97706"
                                : "#166534",
                        }}
                      >
                        {insight.priority} Priority
                      </span>
                      <span
                        style={{
                          padding: "0.25rem 0.75rem",
                          borderRadius: "9999px",
                          fontSize: "0.75rem",
                          fontWeight: "500",
                          backgroundColor: "#e0f2fe",
                          color: "#0369a1",
                        }}
                      >
                        {Math.round(insight.confidence * 100)}% Confidence
                      </span>
                    </div>
                  </div>

                  <p
                    style={{
                      color: "#3f3f46",
                      marginBottom: "1rem",
                      lineHeight: "1.5",
                    }}
                  >
                    {insight.description}
                  </p>

                  <div
                    style={{
                      backgroundColor: "white",
                      border: "1px solid #e2e8f0",
                      borderRadius: "0.375rem",
                      padding: "1rem",
                    }}
                  >
                    <div
                      style={{
                        fontSize: "0.875rem",
                        fontWeight: "600",
                        color: "#18181b",
                        marginBottom: "0.5rem",
                      }}
                    >
                      Recommended Action:
                    </div>
                    <div style={{ color: "#3f3f46", fontSize: "0.875rem" }}>
                      {insight.recommendation}
                    </div>
                    <div
                      style={{
                        marginTop: "0.5rem",
                        fontSize: "0.75rem",
                        color: "#71717a",
                      }}
                    >
                      Expected Impact: {insight.impact}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div
              style={{
                display: "flex",
                gap: "1rem",
                justifyContent: "flex-end",
                marginTop: "2rem",
                borderTop: "1px solid #e4e4e7",
                paddingTop: "1.5rem",
              }}
            >
              <button
                onClick={() => setShowInsights(false)}
                style={{
                  backgroundColor: "#e4e4e7",
                  color: "#3f3f46",
                  borderRadius: "0.375rem",
                  padding: "0.625rem 1.5rem",
                  fontWeight: "500",
                  fontSize: "0.875rem",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                Close
              </button>
              <button
                style={{
                  backgroundColor: "#2b524f",
                  color: "white",
                  borderRadius: "0.375rem",
                  padding: "0.625rem 1.5rem",
                  fontWeight: "500",
                  fontSize: "0.875rem",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                Export Report
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              borderRadius: "0.5rem",
              padding: "2rem",
              maxWidth: "500px",
              width: "90%",
              maxHeight: "80vh",
              overflow: "auto",
            }}
          >
            <h2
              style={{
                fontSize: "1.5rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Forecast Settings
            </h2>
            <p style={{ color: "#71717a", marginBottom: "2rem" }}>
              Advanced configuration options for fine-tuning forecast models.
            </p>
            <div
              style={{
                display: "flex",
                gap: "1rem",
                justifyContent: "flex-end",
              }}
            >
              <button
                onClick={() => setShowSettings(false)}
                style={{
                  backgroundColor: "#e4e4e7",
                  color: "#3f3f46",
                  borderRadius: "0.375rem",
                  padding: "0.625rem 1.5rem",
                  fontWeight: "500",
                  fontSize: "0.875rem",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
