import { useMutation, useQueryClient } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GENERATE_FORECAST_URL, FORECAST_PROGRESS_URL, FORECAST_RESULTS_URL } from "@/constants/urls";

export const usePostForecastGenerate = (
  onSuccess: (data: any) => void,
  onError: (error: any) => void
) => {
  const queryClient = useQueryClient();
  const { mutate: postMutate } = useMutation<any, Error>({
    mutationFn: async (payload: any) => {
      const result = await useDataService.postService(`${GENERATE_FORECAST_URL}`, payload);
      return result;
    },
    onSuccess,
    onError,
  });

  return { postMutate };
};

export const useGetForecastProgress = (
  onSuccess: (data: any) => void,
  onError?: (error: any) => void
) => {

  const { mutate: checkProgress, isPending } = useMutation({
    mutationFn: async (forecastId: string) => {
      const response = await useDataService.getService(`${FORECAST_PROGRESS_URL}/${forecastId}`);
      return response;
    },
    onSuccess,
    onError,
  });

  return { checkProgress, isPending };
};


export const useGetForecastResults = (
  onSuccess: (data: any) => void,
  onError?: (error: any) => void
) => {

  const { mutate: checkResult } = useMutation({
    mutationFn: async (forecastId: string) => {
      const response = await useDataService.getService(`${FORECAST_RESULTS_URL}/${forecastId}`);
      return response;
    },
    onSuccess,
    onError,
  });

  return { checkResult };
};