import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useImportData } from "@/hooks/importData/importDataHooks";
import { Loader2 } from "lucide-react";
import GlobalLoader from "@/components/common/GlobalLoader";

// 1️⃣ Table config with filters
const tableConfig = {
  sales_data: {
    columns: [
      "sale_date",
      "sku_code",
      "location_name",
      "quantity",
      "price_per_unit",
      "total_amount",
    ],
    filters: ["Start_date", "End_date", "Location"],
  },
  skus: {
    columns: ["sku_code", "product_name"],
    filters: ["sku_code"],
  },
  products: {
    columns: ["product_name"],
    filters: ["product_name"],
  },
  locations: {
    columns: ["location_name", "city", "state"],
    filters: ["location_name"],
  },
};

export default function ImportedDataPage() {
  // 3️⃣ UI control states
  const [pendingTable, setPendingTable] = useState("sales_data");
  const [pendingFilters, setPendingFilters] = useState({});
  const [activeTable, setActiveTable] = useState("sales_data");
  const [activeFilters, setActiveFilters] = useState({});
  const [tableData, setTableData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 30;
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const [sortOrder] = useState<"desc">("desc");

  // Import data API call
  const { mutate: fetchImportData } = useImportData();

  const runImportData = (
    table = pendingTable,
    filters = pendingFilters,
    page = currentPage
  ) => {
    const filtersArray = Object.entries(filters)
      .filter(([_, val]) => val !== "" && val !== null)
      .map(([key, value]) => ({
        column: key.toLowerCase(),
        value,
      }));

    setLoading(true);
    fetchImportData(
      {
        table_name: table,
        filters: filtersArray,
        page,
        size: itemsPerPage,
        sort_order: sortOrder,
      },
      {
        onSuccess: (res) => {
          if (res.success) {
            setLoading(false);
            setActiveTable(table);
            setActiveFilters(filters);
            setTableData(res.result.data);
            setTotalItems(res.result.total_items);
            setCurrentPage(res.result.current_page);
          }
        },
        onError: (err) => {
          setLoading(false);
          console.error("API Error:", err);
        },
      }
    );
  };

  const handleTableChange = (val: string) => {
    setPendingTable(val);
    setPendingFilters({});
    setCurrentPage(1);
    runImportData(val, {}, 1);
  };

  const handleFilterChange = (key: string, value: string | Date | null) => {
    setPendingFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilter = () => {
    runImportData();
  };

  // Refetch on page change
  useEffect(() => {
    runImportData();
  }, [currentPage]);

  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const formatTableName = (name: string) =>
    name.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());

  // Helper function to determine if a column should be right-aligned (numeric columns)
  const isNumericColumn = (columnName: string) => {
    const numericColumns = ["quantity", "price_per_unit", "total_amount"];
    return numericColumns.includes(columnName.toLowerCase());
  };

  if (loading) return <GlobalLoader />;

  return (
    <>
      {/* Table Selector */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Imported Data</CardTitle>
        </CardHeader>
        <CardContent>
          <Label htmlFor="table-select">Table Name</Label>
          <Select value={pendingTable} onValueChange={handleTableChange}>
            <SelectTrigger className="w-full max-w-xs">
              <SelectValue placeholder="Select table" />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(tableConfig).map((table) => (
                <SelectItem key={table} value={table}>
                  {formatTableName(table)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Filter & Table Section */}
      <Card>
        <CardHeader>
          <CardTitle>{formatTableName(activeTable)}</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4">
            <div className="flex flex-wrap items-end gap-4 mb-4 w-full">
              {tableConfig[pendingTable]?.filters.map((filterKey) => (
                <div key={filterKey} className="flex-1">
                  <Label>{formatTableName(filterKey)}</Label>
                  {["start_date", "end_date"].includes(
                    filterKey.toLowerCase()
                  ) ? (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal border-zinc-200"
                        >
                          {pendingFilters[filterKey]
                            ? format(new Date(pendingFilters[filterKey]), "PPP")
                            : `Pick ${formatTableName(filterKey)}`}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={
                            pendingFilters[filterKey]
                              ? new Date(pendingFilters[filterKey])
                              : undefined
                          }
                          onSelect={(date) =>
                            handleFilterChange(
                              filterKey,
                              date ? format(date, "MM-dd-yyyy") : ""
                            )
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  ) : (
                    <Input
                      placeholder={`Enter ${formatTableName(filterKey)}`}
                      value={pendingFilters[filterKey] || ""}
                      onChange={(e) =>
                        handleFilterChange(filterKey, e.target.value)
                      }
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="flex items-center mt-1">
              <Button onClick={handleApplyFilter}>Apply Filter</Button>
              <Button
                variant="outline"
                className="ml-3"
                onClick={() => {
                  setPendingFilters({});
                  setCurrentPage(1);
                  runImportData(pendingTable, {}, 1);
                }}
              >
                Clear
              </Button>
            </div>
          </div>
          {/* Table */}
          <div className="rounded-md border overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="bg-zinc-50">
                  {tableConfig[activeTable]?.columns.map((col) => (
                    <th
                      key={col}
                      className={`px-4 py-2 font-medium ${
                        isNumericColumn(col) ? "text-right" : "text-left"
                      }`}
                    >
                      {formatTableName(col)}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {tableData.map((row, idx) => (
                  <tr key={idx} className="border-b last:border-b-0">
                    {tableConfig[activeTable]?.columns.map((col) => (
                      <td
                        key={col}
                        className={`px-4 py-2 ${
                          isNumericColumn(col) ? "text-right" : "text-left"
                        }`}
                      >
                        {col.toLowerCase().includes("date") && row[col]
                          ? format(new Date(row[col]), "MM-dd-yyyy")
                          : row[col]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <span className="text-sm text-muted-foreground">
              Showing {(currentPage - 1) * itemsPerPage + 1} -{" "}
              {Math.min(currentPage * itemsPerPage, tableData.length)} of{" "}
              {tableData.length} rows
            </span>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                className="rounded border"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="rounded border"
                  >
                    {page}
                  </Button>
                )
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  setCurrentPage((p) => Math.min(totalPages, p + 1))
                }
                disabled={currentPage === totalPages}
                className="rounded border"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
