import { useState, useRef, useEffect } from "react";
import {
  X,
  Upload,
  File,
  CheckCircle,
  AlertCircle,
  Trash2,
  FolderOpen,
  CloudUploadIcon,
} from "lucide-react";
import {
  uploadPreSignedUrl,
  usePresignedUrl,
} from "@/hooks/batchFilesUploads/batchFilesUploads";
import { useToast } from "@/hooks/useToast";
import { Button } from "../ui/button";

interface UploadFile {
  id: string;
  file: File;
  status: "pending" | "uploading" | "completed" | "error";
  progress: number;
  error?: string;
}

interface UploadFilesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadComplete: any;
}

export default function UploadFilesModal({
  isOpen,
  onClose,
  onUploadComplete,
}: UploadFilesModalProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [batchName, setBatchName] = useState("");
  const [selectedStore, setSelectedStore] = useState("1");
  const [description, setDescription] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadStatusApiCall, setUploadStatusApiCall] = useState(false);
  const { toasts, removeToast, success, error } = useToast();
  const [stores, setStores] = useState([])
  console.log("uploadFiles", uploadFiles)

  const allowedFileTypes = [".csv", ".xlsx", ".xls"];

  const maxFileSize = 100 * 1024 * 1024; // 10MB

  useEffect(() => {
    if (uploadFiles) {
      const formattedStores = uploadFiles?.map((fileObj, index) => ({
        id: String(index + 1),    // id as index (starting from 1)
        name: fileObj.file.name         // name from uploaded file's id
      }));

      setStores(formattedStores);
    }

  }, [uploadFiles])

  console.log("stores", stores)

  const validateFile = (file: File): string | null => {
    const extension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!allowedFileTypes.includes(extension)) {
      return `File type ${extension} is not supported. Allowed types: ${allowedFileTypes.join(", ")}`;
    }
    if (file.size > maxFileSize) {
      return `File size exceeds 100MB limit. Current size: ${(file.size / 1024 / 1024).toFixed(1)} MB`;
    }
    return null;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadFile[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const error = validateFile(file);

      // Check for duplicates
      const isDuplicate = uploadFiles.some(
        (uf) => uf.file.name === file.name && uf.file.size === file.size
      );
      if (isDuplicate) continue;

      newFiles.push({
        id: Date.now().toString() + i,
        file,
        status: error ? "error" : "pending",
        progress: 0,
        error,
      });
    }

    setUploadFiles((prev) => [...prev, ...newFiles]);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeFile = (fileId: string) => {
    setUploadFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const { mutate: getPresignedUrl } = usePresignedUrl(
    (data) => {
      const getPresignedUrls = data?.result?.data?.files;
      getPresignedUrls.forEach(async ({ upload_url }, index) => {
        const file = uploadFiles[index];
        if (file) {
          const uploadFileAndUrl: any = {
            upload_url: upload_url,
            file: file,
          };
          await uploadFileUsingPresignedUrl(uploadFileAndUrl);
        } else {
          error(
            "Upload Error",
            `No corresponding file found for upload_url ${upload_url}`
          );
        }
      });

      setUploadStatusApiCall(true);
    },
    (errorRes: any) => {
      setIsLoading(false);
      const errorInfo =
        (errorRes as any)?.error?.message ||
        "Something went wrong. Please try again.";
      error("Upload Error", errorInfo);
    }
  );

  const { mutate: uploadFileUsingPresignedUrl } = uploadPreSignedUrl(
    (data) => {
      setIsLoading(false);
      success("Success", `File uploaded successfully to presigned URL`);
      handleClose();
      onUploadComplete(true);
      console.log(":", data);
    },
    (errorRes) => {
      setIsLoading(false);
      error("Error", `Failed to uploaded file on presigned URL: ${errorRes}`);
    }
  );

  const startUpload = async () => {
    if (!uploadFiles) {
      alert("Select a file first");
      return;
    }
    if (uploadFiles.length === 0 || !batchName.trim()) return;

    setIsUploading(true);
    setOverallProgress(0);

    const payloadData: any = {
      batch_name: batchName,
      files: uploadFiles.map((item) => item.file.name),
    };
    getPresignedUrl(payloadData);
    // const validFiles = uploadFiles.filter((f) => f.status !== "error");

    // // Simulate upload process
    // for (let i = 0; i < validFiles.length; i++) {
    //   const file = validFiles[i];

    //   // Update file status to uploading
    //   setUploadFiles((prev) =>
    //     prev.map((f) =>
    //       f.id === file.id ? { ...f, status: "uploading" as const } : f
    //     )
    //   );

    //   // Simulate upload progress
    //   for (let progress = 0; progress <= 100; progress += 10) {
    //     await new Promise((resolve) => setTimeout(resolve, 100));

    //     setUploadFiles((prev) =>
    //       prev.map((f) => (f.id === file.id ? { ...f, progress } : f))
    //     );

    //     // Update overall progress
    //     const fileProgress = (i * 100 + progress) / validFiles.length;
    //     setOverallProgress(fileProgress);
    //   }

    //   // Mark file as completed
    //   setUploadFiles((prev) =>
    //     prev.map((f) =>
    //       f.id === file.id
    //         ? { ...f, status: "completed" as const, progress: 100 }
    //         : f
    //     )
    //   );
    // }

    // // Simulate a brief delay then complete
    // await new Promise((resolve) => setTimeout(resolve, 500));

    // // CHANGED: Send batchName and files to parent
    // onUploadComplete({
    //   batchName,
    //   files: validFiles.map((f) => ({
    //     id: f.id,
    //     fileName: f.file.name,
    //     fileSize: formatFileSize(f.file.size),
    //     status: "pending",
    //   })),
    // });
    // handleClose();
  };

  const handleClose = () => {
    setUploadFiles([]);
    setBatchName("");
    setSelectedStore("1");
    setDescription("");
    setIsUploading(false);
    setOverallProgress(0);
    onClose();
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return (
      <File
        size={16}
        style={{
          color:
            extension === "csv"
              ? "#16a34a"
              : extension === "xlsx" || extension === "xls"
                ? "#16a34a"
                : extension === "json"
                  ? "#2563eb"
                  : "#71717a",
        }}
      />
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const validFiles = uploadFiles.filter((f) => f.status !== "error");
  const canUpload = validFiles.length > 0 && batchName.trim() && !isUploading;

  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "0.5rem",
          maxWidth: "700px",
          width: "90%",
          maxHeight: "90vh",
          overflow: "auto",
          boxShadow:
            "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        }}
      >
        {/* Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderBottom: "1px solid #e2e8f0",
            padding: "1rem",
          }}
        >
          <div
            style={{ display: "flex", alignItems: "center", gap: "0.75rem" }}
          >
            <h2
              style={{
                // fontSize: "1.5rem",
                fontWeight: "600",
                color: "#18181b",
                margin: 0,
              }}
            >
              Upload Supply Chain Data
            </h2>
          </div>
        </div>

        {/* Upload Progress */}
        {isUploading && (
          <div
            style={{
              backgroundColor: "#f8fafc",
              border: "1px solid #e2e8f0",
              borderRadius: "0.5rem",
              padding: "1.5rem",
              marginBottom: "1.5rem",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                fontSize: "0.875rem",
                color: "#71717a",
                marginBottom: "0.5rem",
              }}
            >
              <span>Uploading Files</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <div
              style={{
                height: "0.5rem",
                backgroundColor: "#e2e8f0",
                borderRadius: "0.25rem",
                overflow: "hidden",
              }}
            >
              <div
                style={{
                  height: "100%",
                  backgroundColor: "#2b524f",
                  width: `${overallProgress}%`,
                  transition: "width 0.3s ease",
                }}
              />
            </div>
            <div
              style={{
                fontSize: "0.75rem",
                color: "#71717a",
                marginTop: "0.5rem",
              }}
            >
              Uploading {validFiles.length} file
              {validFiles.length !== 1 ? "s" : ""} to{" "}
              {stores.find((s) => s.id === selectedStore)?.name}...
            </div>
          </div>
        )}

        {/* Batch Information */}
        <div style={{ padding: "1rem", paddingBottom: "0" }}>
          <h3
            style={{
              // fontSize: "1.125rem",
              fontWeight: "600",
              color: "#18181b",
              marginBottom: "1rem",
            }}
          >
            Batch Information
          </h3>

          <div>
            <label
              style={{
                display: "block",
                fontSize: "0.875rem",
                fontWeight: "500",
                color: "#3f3f46",
                marginBottom: "0.25rem",
              }}
            >
              Batch Name<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={batchName}
              onChange={(e) => setBatchName(e.target.value)}
              disabled={isUploading}
              placeholder="e.g. Jan_Batch_2025"
              style={{
                width: "100%",
                padding: "0.625rem 0.75rem",
                border: "1px solid #e4e4e7",
                borderRadius: "0.375rem",
                backgroundColor: isUploading ? "#f9fafb" : "white",
                color: "#18181b",
                fontSize: "0.875rem",
                outline: "none",
              }}
            />
          </div>
        </div>

        {/* File Upload Area */}
        <div style={{ padding: "1rem", paddingBottom: "0" }}>
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            style={{
              border: `2px dashed ${isDragging ? "#2b524f" : "#e4e4e7"}`,
              borderRadius: "0.5rem",
              padding: "2rem",
              textAlign: "center",
              backgroundColor: isDragging
                ? "#f0fdf4"
                : isUploading
                  ? "#f9fafb"
                  : "#fafafa",
              transition: "all 0.2s",
              cursor: isUploading ? "not-allowed" : "pointer",
            }}
            onClick={() => !isUploading && fileInputRef.current?.click()}
          >
            <CloudUploadIcon
              size={48}
              style={{ color: "#71717a", margin: "0 auto 1rem" }}
            />
            <h4
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "0.5rem",
              }}
            >
              {isDragging ? (
                "Drop files here"
              ) : (
                <span>
                  <span style={{ color: "#AB732B" }}>Click to Upload</span> or
                  Drag &amp; Drop
                </span>
              )}
            </h4>
            <p style={{ color: "#71717a", marginBottom: "1rem" }}>
              Supported formats: CSV, XLSX and File size limit is 10MB.
            </p>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={allowedFileTypes.join(",")}
              onChange={(e) => handleFileSelect(e.target.files)}
              style={{ display: "none" }}
              disabled={isUploading}
            />
          </div>
        </div>

        {/* File List */}
        {uploadFiles.length > 0 && (
          <div style={{ padding: "1rem" }}>
            <h3
              style={{
                fontSize: "1.125rem",
                fontWeight: "600",
                color: "#18181b",
                marginBottom: "1rem",
              }}
            >
              Selected Files ({uploadFiles.length})
            </h3>

            <div style={{ maxHeight: "300px", overflow: "auto" }}>
              {uploadFiles.map((uploadFile) => (
                <div
                  key={uploadFile.id}
                  style={{
                    backgroundColor: "#f8fafc",
                    border: "1px solid #e2e8f0",
                    borderRadius: "0.375rem",
                    padding: "1rem",
                    marginBottom: "0.75rem",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "0.75rem",
                        flex: 1,
                      }}
                    >
                      {getFileIcon(uploadFile.file.name)}
                      <div style={{ flex: 1 }}>
                        <div
                          style={{
                            fontSize: "0.875rem",
                            fontWeight: "500",
                            color: "#18181b",
                          }}
                        >
                          {uploadFile.file.name}
                        </div>
                        <div style={{ fontSize: "0.75rem", color: "#71717a" }}>
                          {formatFileSize(uploadFile.file.size)}
                        </div>
                      </div>
                    </div>

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "0.75rem",
                      }}
                    >
                      {/* Status Icon */}
                      {uploadFile.status === "pending" && (
                        <div
                          style={{
                            width: "20px",
                            height: "20px",
                            borderRadius: "50%",
                            backgroundColor: "#f3f4f6",
                          }}
                        />
                      )}
                      {uploadFile.status === "uploading" && (
                        <div
                          style={{
                            width: "16px",
                            height: "16px",
                            border: "2px solid #e5e7eb",
                            borderTop: "2px solid #2b524f",
                            borderRadius: "50%",
                            animation: "spin 1s linear infinite",
                          }}
                        />
                      )}
                      {uploadFile.status === "completed" && (
                        <CheckCircle size={20} style={{ color: "#16a34a" }} />
                      )}
                      {uploadFile.status === "error" && (
                        <AlertCircle size={20} style={{ color: "#dc2626" }} />
                      )}

                      {/* Progress or Remove Button */}
                      {uploadFile.status === "uploading" && (
                        <div
                          style={{
                            fontSize: "0.75rem",
                            color: "#71717a",
                            minWidth: "3rem",
                          }}
                        >
                          {uploadFile.progress}%
                        </div>
                      )}
                      {(uploadFile.status === "pending" ||
                        uploadFile.status === "error") &&
                        !isUploading && (
                          <button
                            onClick={() => removeFile(uploadFile.id)}
                            style={{
                              padding: "0.25rem",
                              color: "#71717a",
                              backgroundColor: "transparent",
                              border: "none",
                              borderRadius: "0.25rem",
                              cursor: "pointer",
                              transition: "all 0.2s",
                            }}
                            title="Remove file"
                          >
                            <Trash2 size={14} color="red" />
                          </button>
                        )}
                    </div>
                  </div>

                  {/* Error Message */}
                  {uploadFile.error && (
                    <div
                      style={{
                        marginTop: "0.5rem",
                        padding: "0.5rem",
                        backgroundColor: "#fef2f2",
                        border: "1px solid #fecaca",
                        borderRadius: "0.25rem",
                        fontSize: "0.75rem",
                        color: "#dc2626",
                      }}
                    >
                      {uploadFile.error}
                    </div>
                  )}

                  {/* Progress Bar */}
                  {uploadFile.status === "uploading" && (
                    <div style={{ marginTop: "0.5rem" }}>
                      <div
                        style={{
                          height: "0.25rem",
                          backgroundColor: "#e2e8f0",
                          borderRadius: "0.125rem",
                          overflow: "hidden",
                        }}
                      >
                        <div
                          style={{
                            height: "100%",
                            backgroundColor: "#2b524f",
                            width: `${uploadFile.progress}%`,
                            transition: "width 0.3s ease",
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div
          style={{
            display: "flex",
            gap: "1rem",
            justifyContent: "flex-end",
            borderTop: "1px solid #e4e4e7",
            padding: "1rem",
          }}
        >
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isUploading}
            style={{
              cursor: isUploading ? "not-allowed" : "pointer",
              opacity: isUploading ? 0.5 : 1,
            }}
          >
            Cancel
          </Button>
          <button
            onClick={startUpload}
            disabled={!canUpload}
            style={{
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
              backgroundColor: canUpload ? "#2b524f" : "#d1d5db",
              color: "white",
              borderRadius: "0.375rem",
              padding: "0.625rem 1.5rem",
              fontWeight: "500",
              fontSize: "0.875rem",
              border: "none",
              cursor: canUpload ? "pointer" : "not-allowed",
            }}
          >
            {isUploading ? (
              <>
                <div
                  style={{
                    width: "1rem",
                    height: "1rem",
                    border: "2px solid rgba(255, 255, 255, 0.3)",
                    borderTop: "2px solid white",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite",
                  }}
                />
                Uploading...
              </>
            ) : (
              <>
                <Upload size={14} />
                Upload Files ({validFiles.length})
              </>
            )}
          </button>
        </div>

        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </div>
  );
}
