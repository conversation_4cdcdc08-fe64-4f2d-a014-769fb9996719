// src/hooks/data-ingestion/useGetBatchList.ts
import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_BATCH_LIST_URL } from "../../constants/urls";
 
interface FileItem {
  file_id: number;
  file_name: string;
  file_format: string;
  upload_status: string;
  import_status: string;
  s3_path: string;
  s3_bucket: string;
}
 
export interface BatchItem {
  batch_id: number;
  batch_name: string;
  import_status: string;
  upload_status: string;
  total_file_count: number;
  notes: string;
  files: FileItem[];
}
 
export interface BatchListResponse {
  success: boolean;
  result: {
    data: {
      data: BatchItem[];
      total: number;
      current_page: number;
      page_size: number;
    };
  };
  message: string;
  code: number;
}
 
export const useGetBatchList = (page: number, size: number, sortOrder: string, p0: string, p1: string, p2: string) => {
  return useQuery({
    queryKey: ["batch-list", page, size, sortOrder],
    queryFn: async () => {
      const endpoint = `${GET_BATCH_LIST_URL}?page=${page}&size=${size}&sort_order=${sortOrder}`;
      return await useDataService.getService(endpoint);
    },
    staleTime: 5 * 60 * 1000, // ✅ don’t re-fetch for 5 minutes
    refetchOnWindowFocus: false, // ✅ don't re-fetch when tab regains focus
  });
};
 