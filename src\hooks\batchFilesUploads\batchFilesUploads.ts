import { useQueryClient,useMutation } from "@tanstack/react-query";
import useDataService from "../../services/useDataService";
import { GETPRESIGNED_URL } from "../../constants/urls";
import axios from "axios";

const getPresignedUrlApi = async (fileData: any) => {
  const response = await useDataService.postService(`${GETPRESIGNED_URL}`, fileData);
  return response;
};

export const usePresignedUrl = (onSuccess?: (data: any) => void, onError?: (error: Error) => void) => {
  return useMutation({
    mutationFn: getPresignedUrlApi,
    onSuccess,
    onError,
  });
};

const uploadFileUsingPresignedUrl = async (fileData: any) => {
  console.log("file data..", fileData.file);
  console.log("")
 const result = await axios.put(fileData.upload_url, fileData.file.file);
      return result.data;
};

export const uploadPreSignedUrl = (onSuccess?: (data: any) => void, onError?: (error: Error) => void) => {
  return useMutation({
    mutationFn: uploadFileUsingPresignedUrl,
    onSuccess,
    onError,
  });
};
  
interface UploadData {
  upload_url: string;
  file: File;
}
