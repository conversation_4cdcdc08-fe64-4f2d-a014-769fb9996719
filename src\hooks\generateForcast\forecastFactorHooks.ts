import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_FORECAST_FACTORS_URL } from "@/constants/urls";

export interface ForecastFactor {
  forecast_factor_id: number;
  factor_name: string;
  display_name: string;
  is_active: boolean;
}

interface GetForecastFactorsResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: ForecastFactor[];
  };
}

export const useGetForecastFactors = () => {
  return useQuery<GetForecastFactorsResponse>({
    queryKey: ["forecast-factors"],
    queryFn: async () => {
      return await useDataService.getService(GET_FORECAST_FACTORS_URL);
    },
   staleTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};
