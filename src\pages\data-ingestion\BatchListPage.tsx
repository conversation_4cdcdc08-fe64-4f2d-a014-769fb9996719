import { useState, useEffect, useMemo } from "react";
import { Upload, Search, ChevronDown, Download, Info } from "lucide-react";
import { useNavigate } from "react-router-dom";
import UploadFilesModal from "@/components/data-ingestion/UploadFilesModal";
import ProcessStepper from "@/components/data-ingestion/ProcessStepper";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

import ValidationPage from "./ValidationPage";
import { useGetBatchList } from "@/hooks/batchList/batchListHooks";
import { useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useGetStoreList } from "@/hooks/store/storeListHooks";
import GlobalLoader from "@/components/common/GlobalLoader";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";

interface FileBatch {
  id: string;
  batchName: string;
  filesCount: number;
  status: "pending" | "validating" | "validated" | "failed";
  files: UploadedFile[];
  expanded?: boolean;
  filesList?: {
    id: string;
    fileName: string;
    mapped: boolean;
  }[];
}
interface BatchWithFiles extends FileBatch {
  expanded?: boolean;
  filesList?: BatchFile[];
}
interface BatchFile {
  id: string;
  fileName: string;
  mapped: boolean;
}
interface UploadedFile {
  id: string;
  fileName: string;
  status: "pending" | "validating" | "validated" | "failed";
}

export default function BatchListPage() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [appliedFilters, setAppliedFilters] = useState<{
    search?: string;
    store?: string;
    status?: string;
  }>({});

  const [expandedBatches, setExpandedBatches] = useState<Set<string>>(
    new Set()
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("Select");
  const [storeFilter, setStoreFilter] = useState("");
  const [storeList, setStoreList] = useState<any[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showValidatePage, setShowValidatePage] = useState(false);
  const [currentStep, setCurrentStep] = useState<1 | 2>(1);
  const [showTable, setShowTable] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const { data: storeListData } = useGetStoreList("desc");
  useEffect(() => {
    if (storeListData) {
      setStoreList(storeListData.result?.data || []);
    }
  }, [storeListData]);

  const { data, isLoading, refetch, error } = useGetBatchList(
    currentPage,
    pageSize,
    "desc",
    appliedFilters.search || "",
    appliedFilters.store || "",
    appliedFilters.status || ""
  );

  const batches: FileBatch[] =
    data?.result?.data?.data?.map((item: any) => ({
      id: item.batch_id.toString(),
      batchName: item.batch_name,
      filesCount: item.total_file_count,
      status: item.upload_status,
      files: item.files.map((f) => ({
        id: f.file_id.toString(),
        fileName: f.file_name,
        status: f.upload_status,
      })),
      filesList: item.files.map((f) => ({
        id: f.file_id.toString(),
        fileName: f.file_name.replace(/\.[^/.]+$/, ""),
        mapped: false,
      })),
    })) ?? [];

  const totalCount = data?.result?.data?.total ?? 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  const getValidationStatus = (batch: BatchWithFiles) => {
    if (!batch.filesList)
      return {
        text: "Not Validated",
        color: "text-red-500 border border-red-500",
      };

    const validatedCount = batch.filesList.filter((f) => f.mapped).length;
    if (validatedCount === 0)
      return {
        text: "Not Validated",
        color: "text-red-500 border border-red-500",
      };
    if (validatedCount === batch.filesList.length)
      return {
        text: "Validated",
        color: "text-green-700 border border-green-700",
      };
    return {
      text: "Partially Validated",
      color: "text-yellow-600 border border-yellow-600",
    };
  };

  const filteredBatches = useMemo(() => {
    if (!batches) return [];

    return batches.filter((batch) => {
      const batchNameMatches = batch.batchName
        .toLowerCase()
        .includes((appliedFilters.search || "").toLowerCase());

      const validationStatus = getValidationStatus(batch).text;
      const statusMatches =
        !appliedFilters.status || appliedFilters.status === "Select"
          ? true
          : appliedFilters.status === validationStatus;

      return batchNameMatches && statusMatches;
    });
  }, [batches, appliedFilters]);

  if (isLoading) return <GlobalLoader />;
  if (error) return <p>Error loading batch list: {error.message}</p>;

  const handleValidatePage = (show: boolean, step: number) => {
    setShowValidatePage(show);
    setCurrentStep(step as 1 | 2);
  };

  const handleUploadComplete = async () => {
    setShowUploadModal(false);
    setShowTable(false);
    setTimeout(async () => {
      const result = await refetch();
      if (result.status === "success") {
        setShowTable(true);
      }
    }, 20000);
  };

  const toggleBatchExpansion = (batchId: string) => {
    setExpandedBatches((prev) => {
      const newSet = new Set(prev);
      newSet.has(batchId) ? newSet.delete(batchId) : newSet.add(batchId);
      return newSet;
    });
  };

  return (
    <>
      {/* Info Label */}
      <div className="mb-4 p-4 rounded bg-cyan-50 border border-cyan-500 text-cyan-700 text-sm font-medium flex items-center justify-between">
        <span className="flex gap-2">
          <Info size={20} className="min-w-[1rem]" />
          <span>
            {" "}
            Start by downloading the template, input the data in the required
            structure, and upload the file using the ‘Upload Data’ button.
          </span>
        </span>
      </div>
      <ProcessStepper currentStep={currentStep} />
      {showValidatePage ? (
        <ValidationPage handleStepper={handleValidatePage} />
      ) : (
        <div className="space-y-6">
          {/* Filter Section */}
          <Card>
            <CardHeader>
              <CardTitle>Filter</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                  <label className="text-sm font-medium mb-1">Search</label>
                  <div className="relative">
                    <Input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="Search batches..."
                      className="pr-10"
                    />
                    <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-1">Store</label>
                  <Select value={storeFilter} onValueChange={setStoreFilter}>
                    <SelectTrigger id="store">
                      <SelectValue placeholder="Select Store" />
                    </SelectTrigger>
                    <SelectContent>
                      {storeList
                        .filter((item) => item?.location_name?.trim() !== "")
                        .map((item, index) => (
                          <SelectItem key={index} value={item.location_id}>
                            {item.location_name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-1">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Select">Select</SelectItem>
                      <SelectItem value="Validated">Validated</SelectItem>
                      <SelectItem value="Not Validated">
                        Not Validated
                      </SelectItem>
                      <SelectItem value="Partially Validated">
                        Partially Validated
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-2 justify-end">
                  <Button
                    onClick={() => {
                      setAppliedFilters({
                        search: searchTerm.trim(),
                        store: storeFilter,
                        status: statusFilter !== "Select" ? statusFilter : "",
                      });
                      setCurrentPage(1);
                    }}
                  >
                    Apply Filter
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm("");
                      setStatusFilter("Select");
                      setStoreFilter("");
                      setAppliedFilters({});
                      setCurrentPage(1);
                    }}
                  >
                    Clear
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* File Batches Section */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-4">
              <CardTitle>File Batches</CardTitle>
              <div>
                <Button variant="outline" className="gap-2 me-4">
                  <Download size={16} /> Download Template
                </Button>
                <Button
                  className="gap-2"
                  onClick={() => setShowUploadModal(true)}
                >
                  <Upload size={16} /> Upload Data
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {batches.length === 0 ? (
                <div className="p-16 text-center text-gray-500 text-lg">
                  Upload Batches to Start Mapping
                </div>
              ) : showTable ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="text-left p-3">
                          Batch Name
                        </TableHead>
                        <TableHead className="text-right p-3">Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBatches.map((batch) => (
                        <React.Fragment key={batch.id}>
                          <TableRow className="border-t">
                            <TableCell className="p-3">
                              <div className="flex gap-4 items-center">
                                <button
                                  onClick={() => toggleBatchExpansion(batch.id)}
                                  aria-label="Toggle"
                                  className="focus:outline-none"
                                  type="button"
                                >
                                  <ChevronDown
                                    className={`border-[1.5px] border-zinc-700 rounded-full w-4 h-4 transition-transform ${
                                      expandedBatches.has(batch.id)
                                        ? "rotate-0"
                                        : "-rotate-90"
                                    }`}
                                  />
                                </button>
                                <span className="text-sm font-medium text-zinc-900">
                                  {batch.batchName}
                                </span>
                                <span
                                  className={`text-xs px-2 py-1 rounded ${getValidationStatus(batch).color}`}
                                >
                                  {getValidationStatus(batch).text}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="p-3 text-right">
                              <Button
                                onClick={() => handleValidatePage(true, 2)}
                                disabled={
                                  getValidationStatus(batch).text ===
                                  "Validated"
                                }
                                className={`${
                                  getValidationStatus(batch).text ===
                                  "Validated"
                                    ? "bg-primary/50 text-white cursor-not-allowed"
                                    : "bg-primary text-white"
                                }`}
                              >
                                Validate
                              </Button>
                            </TableCell>
                          </TableRow>
                          {expandedBatches.has(batch.id) &&
                            batch.filesList?.map((file) => (
                              <TableRow key={file.id} className="bg-white">
                                <TableCell colSpan={2} className="text-sm p-4">
                                  <span className="font-medium text-zinc-900 p-4 pl-5 border-l">
                                    {file.fileName}
                                  </span>
                                </TableCell>
                              </TableRow>
                            ))}
                        </React.Fragment>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <GlobalLoader />
              )}
            </CardContent>
          </Card>

          {/* Upload Modal */}
          <UploadFilesModal
            isOpen={showUploadModal}
            onClose={() => setShowUploadModal(false)}
            onUploadComplete={handleUploadComplete}
          />
        </div>
      )}
    </>
  );
}
