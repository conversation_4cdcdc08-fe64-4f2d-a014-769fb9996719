import { useQuery } from "@tanstack/react-query";
import useDataService from "@/services/useDataService";
import { GET_FORECAST_ALGORITHMS_URL } from "@/constants/urls";

export interface ForecastAlgorithm {
  forecast_algorithm_id: number;
  algorithm_name: string;
  display_name: string;
  is_active: boolean;
}

interface GetForecastAlgorithmsResponse {
  success: boolean;
  code: number;
  message: string;
  result: {
    data: ForecastAlgorithm[];
  };
}

export const useGetForecastAlgorithms = () => {
  return useQuery<GetForecastAlgorithmsResponse>({
    queryKey: ["forecast-algorithms"],
    queryFn: async () => {
      return await useDataService.getService(GET_FORECAST_ALGORITHMS_URL);
    },
    staleTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};
