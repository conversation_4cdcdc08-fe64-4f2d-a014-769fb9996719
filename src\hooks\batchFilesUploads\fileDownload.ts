// import { useQuery } from "@tanstack/react-query";
// import useDataService from "../../services/useDataService";
// import { DOWNLOADFILE_URL } from "../../constants/urls";


// export const useGetMetaDownload = (
//   onSuccessData?: any,
//   params?: any,
//   apiCall?: any,
//   onErrorData?: any,
// ) => {
//   console.log("params",params)
//   const download = useQuery(
//     ["searchbymetadata", params],
//     async () => {
//       const result = await useDataService.getService(
//         `${DOWNLOADFILE_URL}${params?.s3Key}&bucket_name=${params?.bucketName}`,
//       );
//       return result;
//     },
//     {
//       enabled: apiCall,
//       onSuccess: onSuccessData,
//       onError: onErrorData,
//     },
//   );
//   return download;
// };


 