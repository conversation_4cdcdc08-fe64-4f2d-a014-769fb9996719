import { useEffect, use<PERSON>em<PERSON>, useState } from "react";
import Interactive<PERSON>orecast<PERSON><PERSON> from "@/components/forecasting/InteractiveForecastChart";
import MultiSelectDropdown from "@/components/ui/MultiSelectDropdown";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  historicalRevenueData,
  forecastRevenueData,
} from "@/data/forecastMockData";
import { Input } from "@/components/ui/input";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Area,
  ReferenceLine,
} from "recharts";
import {
  usePostForecastGenerate,
  useGetForecastProgress,
  useGetForecastResults,
} from "@/hooks/generateForcast/generateForcastHooks";
import { useGetStoreList } from "@/hooks/store/storeListHooks";
import { useGetSKUList } from "@/hooks/sku/skuListHooks";
import { useGetForecastAlgorithms } from "@/hooks/generateForcast/forecastAlgorithmHooks";
import { useGetForecastFactors } from "@/hooks/generateForcast/forecastFactorHooks";
import { useForecastMeta } from "@/hooks/generateForcast/useForecastMeta";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";


const mockForecastResultData = [
  {
    forecast_result_id: 11,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-01",
    forecast_quantity: 2.06,
    forecast_lower: 2.04,
    forecast_upper: 2.07,
    total_predicted: 2.06,
  },
  {
    forecast_result_id: 12,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-02",
    forecast_quantity: 2.12,
    forecast_lower: 2.06,
    forecast_upper: 2.17,
    total_predicted: 2.12,
  },
  {
    forecast_result_id: 13,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-03",
    forecast_quantity: 32.0,
    forecast_lower: 31.88,
    forecast_upper: 32.09,
    total_predicted: 32.0,
  },
  {
    forecast_result_id: 14,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-04",
    forecast_quantity: 2.28,
    forecast_lower: 2.09,
    forecast_upper: 2.42,
    total_predicted: 2.28,
  },
  {
    forecast_result_id: 15,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-05",
    forecast_quantity: 2.34,
    forecast_lower: 2.08,
    forecast_upper: 2.52,
    total_predicted: 2.34,
  },
  {
    forecast_result_id: 16,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-06",
    forecast_quantity: 2.33,
    forecast_lower: 2.0,
    forecast_upper: 2.6,
    total_predicted: 2.33,
  },
  {
    forecast_result_id: 17,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-07",
    forecast_quantity: 2.33,
    forecast_lower: 1.92,
    forecast_upper: 2.68,
    total_predicted: 2.33,
  },
  {
    forecast_result_id: 18,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-08",
    forecast_quantity: 2.39,
    forecast_lower: 1.91,
    forecast_upper: 2.83,
    total_predicted: 2.39,
  },
  {
    forecast_result_id: 19,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-09",
    forecast_quantity: 2.46,
    forecast_lower: 1.9,
    forecast_upper: 2.96,
    total_predicted: 2.46,
  },
  {
    forecast_result_id: 20,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-10",
    forecast_quantity: 32.33,
    forecast_lower: 31.68,
    forecast_upper: 32.93,
    total_predicted: 32.33,
  },
  {
    forecast_result_id: 21,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-11",
    forecast_quantity: 2.61,
    forecast_lower: 1.86,
    forecast_upper: 3.27,
    total_predicted: 2.61,
  },
  {
    forecast_result_id: 22,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-12",
    forecast_quantity: 2.67,
    forecast_lower: 1.82,
    forecast_upper: 3.43,
    total_predicted: 2.67,
  },
  {
    forecast_result_id: 23,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-13",
    forecast_quantity: 2.67,
    forecast_lower: 1.68,
    forecast_upper: 3.52,
    total_predicted: 2.67,
  },
  {
    forecast_result_id: 24,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-14",
    forecast_quantity: 2.67,
    forecast_lower: 1.6,
    forecast_upper: 3.61,
    total_predicted: 2.67,
  },
  {
    forecast_result_id: 25,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-15",
    forecast_quantity: 2.72,
    forecast_lower: 1.58,
    forecast_upper: 3.77,
    total_predicted: 2.72,
  },
  {
    forecast_result_id: 26,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-16",
    forecast_quantity: 2.79,
    forecast_lower: 1.5,
    forecast_upper: 3.93,
    total_predicted: 2.79,
  },
  {
    forecast_result_id: 27,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-17",
    forecast_quantity: 32.67,
    forecast_lower: 31.3,
    forecast_upper: 33.91,
    total_predicted: 32.67,
  },
  {
    forecast_result_id: 28,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-18",
    forecast_quantity: 2.95,
    forecast_lower: 1.4,
    forecast_upper: 4.31,
    total_predicted: 2.95,
  },
  {
    forecast_result_id: 29,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-19",
    forecast_quantity: 3.0,
    forecast_lower: 1.31,
    forecast_upper: 4.5,
    total_predicted: 3.0,
  },
  {
    forecast_result_id: 30,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-20",
    forecast_quantity: 3.0,
    forecast_lower: 1.13,
    forecast_upper: 4.58,
    total_predicted: 3.0,
  },
  {
    forecast_result_id: 31,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-21",
    forecast_quantity: 3.0,
    forecast_lower: 0.97,
    forecast_upper: 4.68,
    total_predicted: 3.0,
  },
  {
    forecast_result_id: 32,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-22",
    forecast_quantity: 3.06,
    forecast_lower: 0.9,
    forecast_upper: 4.84,
    total_predicted: 3.06,
  },
  {
    forecast_result_id: 33,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-23",
    forecast_quantity: 3.12,
    forecast_lower: 0.84,
    forecast_upper: 5.07,
    total_predicted: 3.12,
  },
  {
    forecast_result_id: 34,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-24",
    forecast_quantity: 33.0,
    forecast_lower: 30.57,
    forecast_upper: 35.12,
    total_predicted: 33.0,
  },
  {
    forecast_result_id: 35,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-25",
    forecast_quantity: 3.28,
    forecast_lower: 0.67,
    forecast_upper: 5.61,
    total_predicted: 3.28,
  },
  {
    forecast_result_id: 36,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-26",
    forecast_quantity: 3.34,
    forecast_lower: 0.64,
    forecast_upper: 5.8,
    total_predicted: 3.34,
  },
  {
    forecast_result_id: 37,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-27",
    forecast_quantity: 3.33,
    forecast_lower: 0.48,
    forecast_upper: 5.93,
    total_predicted: 3.33,
  },
  {
    forecast_result_id: 38,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-02-28",
    forecast_quantity: 3.33,
    forecast_lower: 0.32,
    forecast_upper: 6.07,
    total_predicted: 3.33,
  },
  {
    forecast_result_id: 39,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-03-01",
    forecast_quantity: 3.39,
    forecast_lower: 0.22,
    forecast_upper: 6.25,
    total_predicted: 3.39,
  },
  {
    forecast_result_id: 40,
    forecast_id: 12,
    forecast_name: "Forecast demand_forecast_07",
    sku_id: 1,
    sku_code: "Smart watch",
    location_id: 1,
    location_name: "Indonesia|Java Occidental|Bekasi",
    forecast_date: "2018-03-02",
    forecast_quantity: 3.46,
    forecast_lower: 0.13,
    forecast_upper: 6.5,
    total_predicted: 3.46,
  },
];

interface ChartDataPoint {
  date: string;
  actual?: number;
  value: number;
  confidence_lower?: number;
  confidence_upper?: number;
}

const mockHistoricalData: ChartDataPoint[] = [];

// Custom Tooltip for bounds and values
const ForecastTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const point = payload[0].payload;
    return (
      <div className="bg-white border border-gray-200 rounded shadow p-3 text-xs">
        <div className="font-semibold mb-1">{label}</div>
        {point.actual !== undefined && (
          <div>
            <span className="font-medium text-zinc-700">Actual:</span>{" "}
            <span>{point.actual}</span>
          </div>
        )}
        {point.predicted !== undefined && (
          <div>
            <span className="font-medium text-zinc-700">Forecast:</span>{" "}
            <span>{point.predicted}</span>
          </div>
        )}
        <div>
          <span className="font-medium text-zinc-700">Lower Bound:</span>{" "}
          <span>{point.confidence_lower}</span>
        </div>
        <div>
          <span className="font-medium text-zinc-700">Upper Bound:</span>{" "}
          <span>{point.confidence_upper}</span>
        </div>
      </div>
    );
  }
  return null;
};

export default function GenerateForecastPage() {
  const [showForecastResults, setShowForecastResults] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [forcastResultStatus, setForcastResultStatus] = useState(false);
  const [progress, setProgress] = useState(0);

  // Form states
  const [selectedBatch, setSelectedBatch] = useState("Jan_Batch_2025");
  const [selectedSKUs, setSelectedSKUs] = useState<string[]>([]);
  const [forecastHorizon, setForecastHorizon] = useState("30");
  const [filterBySKU, setFilterBySKU] = useState("SKU");
  const [forecastLabel, setForecastLabel] = useState("");
  const [selectedFactors, setSelectedFactors] = useState<string[]>([]);
  const [forecastId, setForecastId] = useState<string | null>(null);
  const [selectedStoreId, setSelectedStoreId] = useState<string>("");
  const [selectedAlgorithm, setSelectedAlgorithm] = useState<
    string | undefined
  >(undefined);

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [forecastResultData, setForecastResultData] = useState([]);
  const [historicalData, setHistoricalData] = useState([]);
  const [actualsData, setActualsData] = useState([]);
  const { toasts, removeToast, success, error } = useToast();

  const handleRunForecast = () => {
    const newErrors: { [key: string]: string } = {};

    if (!forecastLabel.trim()) newErrors.forecastLabel = "Label is required.";
    if (!selectedStoreId) newErrors.selectedStoreId = "Store is required.";
    if (!selectedAlgorithm)
      newErrors.selectedAlgorithm = "Algorithm is required.";
    if (!forecastHorizon) newErrors.forecastHorizon = "Horizon is required.";
    if (!selectedSKUs || selectedSKUs.length === 0)
      newErrors.selectedSKUs = "At least one SKU must be selected.";

    setErrors(newErrors);

    // Stop if any validation failed
    if (Object.keys(newErrors).length > 0) return;

    setIsGenerating(true);
    setProgress(0);

    const payload: any = {
      algorithm: selectedAlgorithm.toLowerCase(),
      factors: selectedFactors,
      horizon: parseInt(forecastHorizon),
      skus: selectedSKUs.map((id) => parseInt(id)).filter((id) => !isNaN(id)),
      label: forecastLabel,
      location_id: parseInt(selectedStoreId),
    };

    postForecast(payload);

    // // Simulate forecast generation
    // const interval = setInterval(() => {
    //   setProgress((prev) => {
    //     const newProgress = prev + Math.random() * 15;
    //     if (newProgress >= 100) {
    //       setIsGenerating(false);
    //       setShowForecastResults(true);
    //       clearInterval(interval);
    //       return 100;
    //     }
    //     return newProgress;
    //   });
    // }, 200);
  };

  const handleDownloadReport = () => {
    console.log("Downloading forecast report...");

    if (
      !Array.isArray(mockForecastResultData) ||
      mockForecastResultData.length === 0
    )
      return;

    // Define CSV header
    const csvHeader = "Date,SKU,Store,Predicted,Lower Bound,Upper Bound\n";

    const csvRows = mockForecastResultData.map(
      (row) =>
        `="${row.forecast_date}",${row.sku_code},"${row.location_name}",${row.forecast_quantity},${row.forecast_lower},${row.forecast_upper}`
    );

    const csvContent = csvRows.join("\n");
    const blob = new Blob([csvHeader + csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `forecast_${forecastLabel}_${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  // Combine and format your data for Recharts
  const chartData = [
    ...historicalRevenueData.map((d) => ({
      date: d.date,
      actual: d.actual,
      predicted: undefined,
      confidence_lower: d.confidence_lower,
      confidence_upper: d.confidence_upper,
      type: "historical",
    })),
    ...forecastRevenueData.map((d) => ({
      date: d.date,
      actual: undefined,
      predicted: d.predicted,
      confidence_lower: d.confidence_lower,
      confidence_upper: d.confidence_upper,
      type: "forecast",
    })),
  ];

  // Find the first forecast date for the vertical line
  const forecastStartIndex = chartData.findIndex((d) => d.type === "forecast");
  const forecastStartDate =
    forecastStartIndex !== -1 ? chartData[forecastStartIndex].date : undefined;

  // API call for store list, SKU list, forecast algorithm list, forecast factor list  API
  const { stores, skus, algorithms, factors, isMetaLoading, defaultAlgorithm } =
    useForecastMeta();

  useEffect(() => {
    if (!isMetaLoading && defaultAlgorithm && !selectedAlgorithm) {
      setSelectedAlgorithm(defaultAlgorithm);
    }
  }, [isMetaLoading, defaultAlgorithm, selectedAlgorithm]);

  // API call for generate forcast API
  const { postMutate: postForecast } = usePostForecastGenerate(
    (data) => {
      setForecastId(data?.result?.data?.forecastId);
    },
    (errorInfo) => {
      setIsGenerating(false);
      error("Error", `${errorInfo}`);
    }
  );

  // API call for forcast progress API
  useEffect(() => {
    if (!forecastId || !isGenerating) return;
    const interval = setInterval(() => {
      checkProgress(forecastId);
    }, 2000);
    return () => clearInterval(interval); // cleanup
  }, [forecastId, isGenerating]);

  // Reponse handling for forcast progress API
  const { checkProgress, isPending: isCheckingProgress } =
    useGetForecastProgress(
      (data) => {
        const progress = data?.data?.progress || 0;
        setProgress(progress);

        if (
          progress >= 100 &&
          data?.data?.status === "completed" &&
          data?.data?.results_ready
        ) {
          setTimeout(() => {
            checkResult(forecastId);
          }, 500);
        } else if (
          progress >= 100 &&
          (data?.data?.status === "failed" || !data?.data?.results_ready)
        ) {
          setIsGenerating(false);
          error("Error", "Forecast generation failed. Please try again.");
        }
      },
      (errorInfo) => {
        error("Error", `${errorInfo}`);
      }
    );

  // Reponse handling for forcast result API
  const { checkResult } = useGetForecastResults(
    (data) => {
      setShowForecastResults(true)
      const resultArrayForecast = data.result.forecast_data || [];
      const resultArrayHistorical = data.result.historical_data || [];
      const resultArrayActuals = data.result.actuals_data || [];
      setForecastResultData(resultArrayForecast);
      setHistoricalData(resultArrayHistorical);
      setActualsData(resultArrayActuals);
    },
    (errorInfo) => {
      error("Error", `${errorInfo}`);
    }
  );

  // Format forecast data
  const formattedForecastData = forecastResultData.map((d) => ({
    date: d.forecast_date,
    predicted: d.forecast_quantity,
    value: d.forecast_quantity,
    confidence_lower: d.forecast_lower,
    confidence_upper: d.forecast_upper,
  }));

  return (
    <>
      <ToastContainer toasts={toasts} onClose={removeToast} />
      {/* Form Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filter</CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-[16px]">
            {/* Label */}
            <div>
              <Label htmlFor="forecast-label">Forecast Label</Label>
              <Input
                id="forecast-label"
                type="text"
                value={forecastLabel}
                onChange={(e) => {
                  setForecastLabel(e.target.value);
                  if (errors.forecastLabel) {
                    setErrors((prev) => ({ ...prev, forecastLabel: "" }));
                  }
                }}
                placeholder="Enter forecast label"
              />
              {errors.forecastLabel && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.forecastLabel}
                </p>
              )}
            </div>

            {/* Store */}
            <div>
              <Label htmlFor="store">Select Store</Label>
              <Select
                value={selectedStoreId}
                onValueChange={(value) => {
                  setSelectedStoreId(value);
                  if (errors.selectedStoreId) {
                    setErrors((prev) => ({ ...prev, selectedStoreId: "" }));
                  }
                }}
              >
                <SelectTrigger id="store">
                  <SelectValue
                    placeholder={
                      isMetaLoading ? "Loading Stores..." : "Select store"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {stores.map((store) => (
                    <SelectItem
                      key={store.location_id}
                      value={String(store.location_id)}
                    >
                      {store.location_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.selectedStoreId && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.selectedStoreId}
                </p>
              )}
            </div>

            {/* SKU */}
            <div>
              <Label htmlFor="sku">Select SKU</Label>
              <Select
                value={selectedSKUs[0] ?? ""}
                onValueChange={(value) => {
                  setSelectedSKUs([value]);
                  if (errors.selectedSKUs) {
                    setErrors((prev) => ({ ...prev, selectedSKUs: "" }));
                  }
                }}
              >
                <SelectTrigger id="sku">
                  <SelectValue
                    placeholder={
                      isMetaLoading ? "Loading SKUs..." : "Select SKU"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {skus.map((sku) => (
                    <SelectItem key={sku.sku_id} value={sku.sku_id.toString()}>
                      {sku.sku_code}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.selectedSKUs && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.selectedSKUs}
                </p>
              )}
            </div>

            {/* Algorithm */}
            <div>
              <Label htmlFor="algorithm">Select Algorithm</Label>
              <Select
                value={selectedAlgorithm ?? ""}
                onValueChange={setSelectedAlgorithm}
              >
                <SelectTrigger id="algorithm">
                  <SelectValue
                    placeholder={
                      isMetaLoading
                        ? "Loading algorithm..."
                        : "Select algorithm"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {algorithms.map((algo) => (
                    <SelectItem
                      key={algo.forecast_algorithm_id}
                      value={algo.algorithm_name}
                      disabled={!algo.is_active}
                    >
                      {algo.display_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.selectedAlgorithm && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.selectedAlgorithm}
                </p>
              )}
            </div>

            {/* Forecast Horizon Days */}
            <div>
              <Label htmlFor="horizon">Forecast Horizon (in days)</Label>
              <Select
                value={forecastHorizon}
                onValueChange={setForecastHorizon}
              >
                <SelectTrigger id="horizon">
                  <SelectValue placeholder="Select horizon" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7</SelectItem>
                  <SelectItem value="14">14</SelectItem>
                  <SelectItem value="30">30</SelectItem>
                </SelectContent>
              </Select>
              {errors.forecastHorizon && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.forecastHorizon}
                </p>
              )}
            </div>

            {/* Forecast Factors Dropdown */}
            <div>
              <Label>Include Factors</Label>
              <MultiSelectDropdown
                className="custom-multi-select"
                options={
                  factors?.map((factor) => ({
                    value: factor.factor_name,
                    label: factor.display_name,
                    isDisabled: !factor.is_active,
                  })) || []
                }
                value={selectedFactors}
                onChange={setSelectedFactors}
                placeholder={
                  isMetaLoading ? "Loading factors..." : "Select factors"
                }
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-4">
            <Button onClick={handleRunForecast} disabled={isGenerating}>
              {isGenerating ? "Generating..." : "Run Forecast"}
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                setSelectedBatch("");
                setSelectedAlgorithm("");
                setSelectedStoreId("");
                setSelectedSKUs([]);
                setForecastHorizon("");
                setFilterBySKU("");
                setForecastLabel("");
                setSelectedFactors([]);
                setShowForecastResults(false);
                setIsGenerating(false);
                setProgress(0);
              }}
            >
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress Bar */}
      {isGenerating && (
        <Card className="mb-6">
          <CardContent>
            <div className="flex justify-between text-xs mb-2">
              <span>Generating Forecast</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-primary transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
            <div className="text-xs mt-2">
              Processing {forecastHorizon} days using {selectedAlgorithm} model
              for {selectedSKUs.length} SKU
              {selectedSKUs.length !== 1 ? "s" : ""}...
            </div>
          </CardContent>
        </Card>
      )}

      {/* Forecast Results */}
      {showForecastResults && (
        <>
          {/* Chart Section */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Forecast Results</CardTitle>
            </CardHeader>
            <CardContent>
              <InteractiveForecastChart
                actualsData={[]}
                historicalData={historicalData}
                forecastData={formattedForecastData}
                isLoading={isGenerating}
              />
            </CardContent>
          </Card>

          {/* Forecast Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold ">
                Forecast Table
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="px-6 py-3 text-left text-xs font-medium tracking-wider">
                        Date
                      </TableHead>
                      <TableHead className="px-6 py-3 text-left text-xs font-medium tracking-wider">
                        Product/SKU
                      </TableHead>
                      <TableHead className="px-6 py-3 text-left text-xs font-medium tracking-wider">
                        Store
                      </TableHead>
                      <TableHead className="px-6 py-3 text-left text-xs font-medium tracking-wider">
                        Forecast (y)
                      </TableHead>
                      <TableHead className="px-6 py-3 text-left text-xs font-medium tracking-wider">
                        Lower Bound(y-lower)
                      </TableHead>
                      <TableHead className="px-6 py-3 text-left text-xs font-medium tracking-wider">
                        Upper Bound(y-Upper)
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {forecastResultData.map((row, index) => (
                      <TableRow key={index} className="hover:bg-gray-50">
                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm">
                          {row.forecast_date}
                        </TableCell>
                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm">
                          {row.sku_code}
                        </TableCell>
                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm">
                          {row.location_name}
                        </TableCell>
                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-right">
                          {row.forecast_quantity}
                        </TableCell>
                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-right">
                          {row.forecast_lower}
                        </TableCell>
                        <TableCell className="px-6 py-4 whitespace-nowrap text-sm text-right">
                          {row.forecast_upper}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <div className="pt-6 flex justify-end">
                <Button onClick={handleDownloadReport}>
                  Download Forecast Table
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </>
  );
}
