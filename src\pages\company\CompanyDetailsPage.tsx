import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Pencil, Save } from "lucide-react";
import { Country, State, City } from "country-state-city";
import {
  useGetCompanyDetails,
  useUpdateCompanyDetails,
  useGetCompanyTypes,
} from "../../hooks/CompanyDetails/useCompanyDetails"; // Adjust the import based on your file structure
import { getUserTenantId } from "@/utils/userInfo";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";

const schema = yup.object({
  companyName: yup.string().required("Company name is required"),
  companyType: yup.string().required("Company type is required"),
  numberOfStores: yup.string().required("Number Of Stores is required"),
  websiteURL: yup.string(),
  country: yup.string().required("Country is required"),
  state: yup.string().required("State / Region is required"),
  city: yup.string().required("City is required"),
  streetAddress: yup.string().required("Street Address is required"),
  zipCode: yup.string().required("ZIP/Postal Code is required"),
  registrationNumber: yup
    .string()
    .required("Tax ID/ Business Registration Number is required"),
});

export interface CompanyDetailsFormData {
  companyName: string;
  companyType: string;
  numberOfStores: string;
  websiteURL: string;
  country: string;
  state: string;
  city: string;
  streetAddress: string;
  zipCode: string;
  registrationNumber: string;
}

export default function CompanyDetailsPage() {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CompanyDetailsFormData>({
    resolver: yupResolver(schema),
  });
  const tenantId = getUserTenantId();
  const selectedCountryCode = watch("country");
  const selectedStateCode = watch("state");
  const [isLoading, setIsLoading] = useState(false);
  const [countries, setCountries] = useState(Country.getAllCountries());
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const { toasts, removeToast, success, error } = useToast();


  const queryClient = useQueryClient();
  const {
    data,
    isLoading: isCompanyDetailsLoading,
    refetch,
  } = useGetCompanyDetails(tenantId);
  const CompanyDetails = data?.result?.data || {};
  const updateMutation = useUpdateCompanyDetails();
  const { data: companyTypes, isLoading: isCompanyTypesLoading } =
    useGetCompanyTypes();


  useEffect(() => {
    if (selectedCountryCode) {
      const statesData = State.getStatesOfCountry(selectedCountryCode);
      setStates(statesData);
      setValue("state", "");
      setValue("city", "");
      setCities([]);
    }
  }, [selectedCountryCode, setValue]);

  useEffect(() => {
    if (selectedStateCode && selectedCountryCode) {
      const citiesData = City.getCitiesOfState(
        selectedCountryCode,
        selectedStateCode
      );
      setCities(citiesData);
      setValue("city", "");
    }
  }, [selectedStateCode, selectedCountryCode, setValue]);

  useEffect(() => {
    console.log("Company Details Page Loaded", data?.result.data);
    if (CompanyDetails) {
      // Populate form with existing company details
      setValue("companyName", CompanyDetails.tenant_name);
      setValue("companyType", CompanyDetails.companyType);
      setValue("numberOfStores", CompanyDetails.number_of_locations);
      setValue("websiteURL", CompanyDetails.website_url);
      setValue("country", CompanyDetails.country);
      setValue("state", CompanyDetails.state);
      setValue("city", CompanyDetails.city);
      setValue("streetAddress", CompanyDetails.street_address);
      setValue("zipCode", CompanyDetails.postal_code);
      setValue("registrationNumber", CompanyDetails.tax_id);
    }
  }, [data, setValue]);

  // 1. Set country as soon as CompanyDetails is available
  useEffect(() => {
    if (CompanyDetails && CompanyDetails.country) {
      setValue("country", CompanyDetails.country);
    }
  }, [CompanyDetails, setValue]);

  // 2. When states are loaded and CompanyDetails.state is available, set state
  useEffect(() => {
    if (
      CompanyDetails &&
      CompanyDetails.state &&
      states.length > 0 &&
      states.some((s) => s.isoCode === CompanyDetails.state)
    ) {
      setValue("state", CompanyDetails.state);
    }
  }, [CompanyDetails, states, setValue]);

  // 3. When cities are loaded and CompanyDetails.city is available, set city
  useEffect(() => {
    if (
      CompanyDetails &&
      CompanyDetails.city &&
      cities.length > 0 &&
      cities.some((c) => c.name === CompanyDetails.city)
    ) {
      setValue("city", CompanyDetails.city);
    }
  }, [CompanyDetails, cities, setValue]);

  function handleSetDefaultValues() {
    if (CompanyDetails) {
      setValue("companyName", CompanyDetails.tenant_name || "");
      setValue("companyType", CompanyDetails.company_type || "");
      setValue(
        "numberOfStores",
        CompanyDetails.number_of_locations?.toString() || ""
      );
      setValue("websiteURL", CompanyDetails.website_url || "");
      setValue("streetAddress", CompanyDetails.street_address || "");
      setValue("zipCode", CompanyDetails.postal_code || "");
      setValue("registrationNumber", CompanyDetails.tax_id || "");
    }
  }

  // 4. Set other fields (not country/state/city) as before
  useEffect(() => {
    if (CompanyDetails) {
      handleSetDefaultValues()
    }
  }, [CompanyDetails, setValue]);

  const onSubmit = async (formData: CompanyDetailsFormData) => {
    if (!isEditMode) return;
    setIsLoading(true);
    try {
      await updateMutation.mutateAsync({
        tenantId,
        data: {
          tenant_name: formData.companyName,
          company_type: formData.companyType,
          number_of_locations: Number(formData.numberOfStores),
          website_url: formData.websiteURL,
          country: formData.country,
          state: formData.state,
          city: formData.city,
          street_address: formData.streetAddress,
          postal_code: formData.zipCode,
          tax_id: formData.registrationNumber,
        },
      });
      success("Success", `Company Details Updated successful.`);

      setIsEditMode(false);
      await refetch(); // <-- Refetch latest company details after update
    } catch (err) {
      console.log("company detail error", err)
      // handle error
      error("Error", `Something went wrong.`);

    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditMode(false)
    handleSetDefaultValues()
  }

  return (
    <>
      <div className="grid gap-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-4">
            <CardTitle>Company Details</CardTitle>
            {!isEditMode && (
              <Button
                type="button"
                className="gap-2 px-3 h-[34px]"
                onClick={() => setIsEditMode(true)}
              >
                <Pencil size={14} />
                Edit
              </Button>
            )}
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                {/* Company Name */}
                <div>
                  <Label htmlFor="companyName">Company Name<span className="text-red-500">*</span></Label>
                  <Input
                    id="companyName"
                    type="text"
                    {...register("companyName")}
                    disabled={!isEditMode}
                    className={errors.companyName ? "border-destructive" : ""}
                    placeholder="Enter company name"
                  />
                  {errors.companyName && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.companyName.message}
                    </p>
                  )}
                </div>
                {/* Company Type */}
                <div>
                  <Label htmlFor="companyType">Company Type<span className="text-red-500">*</span></Label>
                  <Select
                    value={watch("companyType")}
                    onValueChange={(val) => setValue("companyType", val)}
                    disabled={!isEditMode}
                  >
                    <SelectTrigger
                      id="companyType"
                      className={errors.companyType ? "border-destructive" : ""}
                    >
                      <SelectValue
                        placeholder={
                          isCompanyTypesLoading ? "Loading..." : "Select type"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {isCompanyTypesLoading ? (
                        <SelectItem value="loading" disabled>
                          Loading...
                        </SelectItem>
                      ) : (
                        companyTypes?.map((type) => (
                          <SelectItem
                            key={type.industry_id}
                            value={type.industry_id.toString()}
                          >
                            {type.industry_name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.companyType && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.companyType.message}
                    </p>
                  )}
                </div>
                {/* Number of Stores */}
                <div>
                  <Label htmlFor="numberOfStores">Number of Stores</Label>
                  <Input
                    id="numberOfStores"
                    type="text"
                    {...register("numberOfStores")}
                    disabled={!isEditMode}
                    className={
                      errors.numberOfStores ? "border-destructive" : ""
                    }
                    placeholder="Enter number of stores"
                  />
                  {errors.numberOfStores && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.numberOfStores.message}
                    </p>
                  )}
                </div>
                {/* Website URL */}
                <div>
                  <Label htmlFor="websiteURL">Website URL</Label>
                  <Input
                    id="websiteURL"
                    type="text"
                    {...register("websiteURL")}
                    disabled={!isEditMode}
                    className={errors.websiteURL ? "border-destructive" : ""}
                    placeholder="Enter website URL"
                  />
                  {errors.websiteURL && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.websiteURL.message}
                    </p>
                  )}
                </div>
                {/* Country */}
                <div>
                  <Label htmlFor="country">Country<span className="text-red-500">*</span></Label>
                  <Select
                    value={watch("country")}
                    onValueChange={(val) => setValue("country", val)}
                    disabled={!isEditMode}
                  >
                    <SelectTrigger
                      id="country"
                      className={errors.country ? "border-destructive" : ""}
                    >
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem
                          key={country.isoCode}
                          value={country.isoCode}
                        >
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.country && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.country.message}
                    </p>
                  )}
                </div>
                {/* State / Region */}
                <div>
                  <Label htmlFor="state">State / Region<span className="text-red-500">*</span></Label>
                  <Select
                    value={watch("state")}
                    onValueChange={(val) => setValue("state", val)}
                    disabled={!states.length || !isEditMode}
                  >
                    <SelectTrigger
                      id="state"
                      className={errors.state ? "border-destructive" : ""}
                      disabled={!states.length || !isEditMode}
                    >
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>
                      {states.map((state) => (
                        <SelectItem key={state.isoCode} value={state.isoCode}>
                          {state.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.state && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.state.message}
                    </p>
                  )}
                </div>
                {/* City */}
                <div>
                  <Label htmlFor="city">City<span className="text-red-500">*</span></Label>
                  <Select
                    value={watch("city")}
                    onValueChange={(val) => setValue("city", val)}
                    disabled={!cities.length || !isEditMode}
                  >
                    <SelectTrigger
                      id="city"
                      className={errors.city ? "border-destructive" : ""}
                      disabled={!cities.length || !isEditMode}
                    >
                      <SelectValue placeholder="Select city" />
                    </SelectTrigger>
                    <SelectContent>
                      {cities.map((city) => (
                        <SelectItem key={city.name} value={city.name}>
                          {city.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.city && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.city.message}
                    </p>
                  )}
                </div>
                {/* Street Address */}
                <div>
                  <Label htmlFor="streetAddress">Street Address</Label>
                  <Input
                    id="streetAddress"
                    type="text"
                    {...register("streetAddress")}
                    disabled={!isEditMode}
                    className={errors.streetAddress ? "border-destructive" : ""}
                    placeholder="Enter street address"
                  />
                  {errors.streetAddress && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.streetAddress.message}
                    </p>
                  )}
                </div>
                {/* ZIP/Postal Code */}
                <div>
                  <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                  <Input
                    id="zipCode"
                    type="text"
                    {...register("zipCode")}
                    disabled={!isEditMode}
                    className={errors.zipCode ? "border-destructive" : ""}
                    placeholder="Enter ZIP/Postal Code"
                  />
                  {errors.zipCode && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.zipCode.message}
                    </p>
                  )}
                </div>
                {/* Tax ID/Business Registration Number */}
                <div>
                  <Label htmlFor="registrationNumber">
                    Tax ID/ Business Registration Number
                  </Label>
                  <Input
                    id="registrationNumber"
                    type="text"
                    {...register("registrationNumber")}
                    disabled={!isEditMode}
                    className={
                      errors.registrationNumber ? "border-destructive" : ""
                    }
                    placeholder="Enter registration number"
                  />
                  {errors.registrationNumber && (
                    <p className="mt-1 text-xs text-destructive">
                      {errors.registrationNumber.message}
                    </p>
                  )}
                </div>
              </div>
              {/* Save/Update Button */}
              {isEditMode && (
                <div className="mt-8 pt-4 border-t flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="gap-2"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="gap-2"
                    onMouseOver={(e) => {
                      if (!isLoading) {
                        (e.target as HTMLButtonElement).style.backgroundColor =
                          "#1f413e";
                      }
                    }}
                    onMouseOut={(e) => {
                      if (!isLoading) {
                        (e.target as HTMLButtonElement).style.backgroundColor =
                          "#2b524f";
                      }
                    }}
                  >
                    <Save size={16} />
                    {isLoading ? "Updating..." : "Update"}
                  </Button>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </div>
    </>
  );
}
